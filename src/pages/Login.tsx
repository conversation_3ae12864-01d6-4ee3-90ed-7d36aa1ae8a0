import {useState} from "react";
import {zodResolver} from "@hookform/resolvers/zod";
import {useForm} from "react-hook-form";
import * as z from "zod";
import {Button} from "@/components/ui/button";
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage,} from "@/components/ui/form";
import {Input} from "@/components/ui/input";
import {useLocation, useNavigate} from "react-router-dom";
import SignupForm from "@/components/auth/SignupForm";
import {useAuth} from "@/contexts/AuthContext";
import {Loader2} from "lucide-react";

const formSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

const Login = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [unconfirmedEmail, setUnconfirmedEmail] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isLoading, resendConfirmationCode, confirmSignUpWithCode } = useAuth();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsSubmitting(true);
      await login(values.email, values.password);

      // Redirect to the page they were trying to access, or home if none
      const from = location.state?.from?.pathname || "/home";
      navigate(from, { replace: true });
    } catch (error) {
      console.error("Login error:", error.name);

      // Check if this is an unconfirmed user error
      if (error.name === 'UserNotConfirmedException') {
        // Store the email for verification
        setUnconfirmedEmail(error.email);
        // Show verification form
        setShowVerification(true);
      }
      // Other errors are handled in the AuthContext
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      // Confirm signup with code
      await confirmSignUpWithCode(unconfirmedEmail, verificationCode);

      // Reset verification state
      setShowVerification(false);
      setVerificationCode("");

      // Switch back to login form
      setIsLogin(true);
    } catch (error) {
      console.error("Verification error:", error);
      // Error is handled in the AuthContext
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendCode = async () => {
    try {
      setIsSubmitting(true);
      await resendConfirmationCode(unconfirmedEmail);
    } catch (error) {
      console.error("Error resending code:", error);
      // Error is handled in the AuthContext
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignIn = () => {
    console.log("Google sign in clicked");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-lg">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {isLogin ? "Sign in to your account" : "Create new account"}
          </h2>
        </div>

        {/*<Button*/}
        {/*  variant="outline"*/}
        {/*  onClick={handleGoogleSignIn}*/}
        {/*  className="w-full flex items-center justify-center gap-2 mb-4"*/}
        {/*>*/}
        {/*  <svg className="w-4 h-4" viewBox="0 0 24 24">*/}
        {/*    <path*/}
        {/*      fill="currentColor"*/}
        {/*      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"*/}
        {/*    />*/}
        {/*    <path*/}
        {/*      fill="currentColor"*/}
        {/*      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"*/}
        {/*    />*/}
        {/*    <path*/}
        {/*      fill="currentColor"*/}
        {/*      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"*/}
        {/*    />*/}
        {/*    <path*/}
        {/*      fill="currentColor"*/}
        {/*      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"*/}
        {/*    />*/}
        {/*  </svg>*/}
        {/*  Continue with Google*/}
        {/*</Button>*/}

        {/*<div className="relative my-6">*/}
        {/*  <div className="absolute inset-0 flex items-center">*/}
        {/*    <div className="w-full border-t border-gray-300" />*/}
        {/*  </div>*/}
        {/*  <div className="relative flex justify-center text-sm">*/}
        {/*    <span className="px-2 bg-white text-gray-500">Or continue with email</span>*/}
        {/*  </div>*/}
        {/*</div>*/}

        {isLogin ? (
          showVerification ? (
            <div className="space-y-4">
              <form onSubmit={handleVerificationSubmit} className="space-y-4">
                <h3 className="text-lg font-medium">Verify your email</h3>
                <p className="text-sm text-gray-500">
                  We've sent a verification code to {unconfirmedEmail}. Please enter it below.
                </p>
                <div>
                  <label htmlFor="verification-code" className="block text-sm font-medium text-gray-700">Verification Code</label>
                  <div className="mt-1">
                    <input
                      id="verification-code"
                      name="verification-code"
                      type="text"
                      autoComplete="off"
                      required
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="123456"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-3">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      "Verify Account"
                    )}
                  </Button>
                  <div className="text-center">
                    <button
                      type="button"
                      onClick={() => {
                        setShowVerification(false);
                        setVerificationCode("");
                        form.reset();
                      }}
                      className="text-sm text-primary hover:underline"
                    >
                      Go back to sign in
                    </button>
                  </div>
                  <div className="text-center">
                    <button
                      type="button"
                      onClick={handleResendCode}
                      className="text-sm text-primary hover:underline"
                      disabled={isSubmitting}
                    >
                      Resend verification code
                    </button>
                  </div>
                </div>
              </form>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="mt-8 space-y-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting || isLoading}
                >
                  {isSubmitting || isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    "Sign in"
                  )}
                </Button>
              </form>
            </Form>
          )
        ) : (
          <SignupForm onSignupComplete={() => setIsLogin(true)} />
        )}

        <div className="text-center mt-4">
          {!showVerification && (
            <button
              type="button"
              onClick={() => {
                setIsLogin(!isLogin);
                // Reset the login form when switching to signup
                if (isLogin) {
                  form.reset();
                }
              }}
              className="text-sm text-primary hover:underline"
            >
              {isLogin ? "Need an account? Sign up" : "Already have an account? Sign in"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Login;

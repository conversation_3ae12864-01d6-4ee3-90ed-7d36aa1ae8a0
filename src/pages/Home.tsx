import {MainLayout} from "@/components/layout/MainLayout";
import {Award, Book, MonitorPlay, Video} from "lucide-react";
import {HeroSection} from "@/components/home/<USER>";
import {FeatureCard} from "@/components/home/<USER>";

const Home = () => {
  const features = [
    {
      title: "Easy Video Creation",
      description: "Upload your content and generate professional lecture videos in minutes using our intuitive interface.",
      icon: Video
    },
    {
      title: "Educational Templates",
      description: "Access a wide range of templates designed specifically for educational content.",
      icon: Book
    },
    {
      title: "Interactive Elements",
      description: "Add engaging elements to your videos to enhance student learning experience.",
      icon: MonitorPlay
    },
    {
      title: "Professional Quality",
      description: "Generate high-quality videos that meet educational standards and keep students engaged.",
      icon: Award
    }
  ];

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12">
        <HeroSection />
        
        <div className="grid md:grid-cols-2 gap-6 mt-12">
          {features.map((feature) => (
            <FeatureCard
              key={feature.title}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
            />
          ))}
        </div>

        {/* Tutorial Section */}
        <div className="mt-16 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            How to create a Video Generation Session
          </h2>
          <div className="flex justify-center">
            <iframe
              width="700px"
              height="400px"
              src="https://embed.app.guidde.com/playbooks/3d47kkuSv1gfHNN8BLiYPJ"
              title="How to create a new video generation session"
              frameBorder="0"
              referrerPolicy="unsafe-url"
              allowFullScreen={true}
              allow="clipboard-write"
              sandbox="allow-popups allow-popups-to-escape-sandbox allow-scripts allow-forms allow-same-origin allow-presentation"
              style={{borderRadius: '10px'}}
            />
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Home;

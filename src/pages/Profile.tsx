import {MainLayout} from "@/components/layout/MainLayout";
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import {Badge} from "@/components/ui/badge";
import {But<PERSON>} from "@/components/ui/button";
import {AlertCircle, Clock, CreditCard, Settings, User, Video} from "lucide-react";
import {useNavigate} from "react-router-dom";
import {useQuery} from "@tanstack/react-query";
import {Alert, AlertDescription} from "@/components/ui/alert";
import {Skeleton} from "@/components/ui/skeleton";
import {Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {format} from "date-fns";
import {API_BASE_URL, authenticatedFetch} from "@/utils/authUtils";

// Define the user information interface
interface UserInformation {
  name: string;
  email: string;
  token: string;
  plan: string;
}

// Define the token history interface
interface TokenHistoryItem {
  history: string;
  time: string;
  mode: "Buy" | "Session";
  token: number;
}

// Function to fetch user information
const fetchUserInformation = async (): Promise<UserInformation> => {
  const response = await authenticatedFetch(
    `${API_BASE_URL}/users/information`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch user information");
  }

  const data = await response.json();

  // Check if the data is nested in a 'body' property (common in AWS API Gateway responses)
  if (data.body) {
    return data.body;
  }

  return data;
};

// Function to fetch token history
const fetchTokenHistory = async (): Promise<TokenHistoryItem[]> => {
  const response = await authenticatedFetch(
    `${API_BASE_URL}/history/list`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch token history");
  }

  const data = await response.json();

  // Check if the data is nested in a 'body' property
  if (data.body) {
    return data.body;
  }
  return data;
};

// Token History Component
const TokenHistory = () => {
  // Fetch token history using React Query
  const { data: history, isLoading, error } = useQuery({
    queryKey: ['tokenHistory'],
    queryFn: fetchTokenHistory,
    retry: 2,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000,
  });

  // Function to format the date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      return dateString;
    }
  };

  // Function to get the icon based on the mode
  const getModeIcon = (mode: string) => {
    switch (mode) {
      case 'Buy':
        return <CreditCard className="h-4 w-4 text-green-500" />;
      case 'Session':
        return <Video className="h-4 w-4 text-blue-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load token history. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  // Sort history by date in descending order (most recent first)
  const sortedHistory = history ? [...history].sort((a, b) => {
    const dateA = new Date(a.time);
    const dateB = new Date(b.time);
    return dateB.getTime() - dateA.getTime();
  }) : [];

  return (
    <div>
      <Table>
        <TableCaption>Your token usage history</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Type</TableHead>
            <TableHead className="text-right">Tokens</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedHistory && sortedHistory.length > 0 ? (
            sortedHistory.map((item) => (
              <TableRow key={item.history}>
                <TableCell>{formatDate(item.time)}</TableCell>
                <TableCell className="flex items-center">
                  {getModeIcon(item.mode)}
                  <span className="ml-2">{item.mode}</span>
                </TableCell>
                <TableCell className={`text-right font-medium ${item.token > 0 ? 'text-green-600' : item.token < 0 ? 'text-red-600' : ''}`}>
                  {item.token}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={3} className="text-center py-4">
                No token history available
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

const Profile = () => {
  const navigate = useNavigate();

  // Fetch user information using React Query
  const { data: user, isLoading, error } = useQuery({
    queryKey: ['userInformation'],
    queryFn: fetchUserInformation,
    retry: 2, // Retry failed requests up to 2 times
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
  });

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 gradient-text">My Profile</h1>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to load user information. Please try again later.
              </AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User Info Card */}
            <Card className="glass-effect">
              <CardHeader className="flex flex-col items-center">
                <Avatar className="h-24 w-24 mb-3">
                  <AvatarImage src="" alt={user?.name || "User"} />
                  <AvatarFallback className="bg-gradient-to-r from-[#33C3F0] to-[#1EAEDB] text-xl text-white">
                    <User className="h-12 w-12" />
                  </AvatarFallback>
                </Avatar>
                {isLoading ? (
                  <>
                    <Skeleton className="h-6 w-32 mb-2" />
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-5 w-20" />
                  </>
                ) : (
                  <>
                    <CardTitle className="text-xl text-center">{user?.name || "No name available"}</CardTitle>
                    <CardDescription className="text-center">{user?.email || "No email available"}</CardDescription>
                    <Badge variant="secondary" className="mt-2">{user?.plan || "Free"} Plan</Badge>
                  </>
                )}
              </CardHeader>
              <CardContent className="text-center">
                <Button
                  variant="outline"
                  className="mt-4 w-full"
                  onClick={() => navigate("/settings")}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Edit Profile
                </Button>
              </CardContent>
            </Card>

            {/* Account Details Card */}
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle>Account Details</CardTitle>
                <CardDescription>Your account information and resources.</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-4">
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-white/50 rounded-md p-4 shadow-sm">
                      <h3 className="font-medium text-primary">Email Address</h3>
                      <p className="text-lg font-semibold mt-2">{user?.email || "No email available"}</p>
                    </div>
                    <div className="bg-white/50 rounded-md p-4 shadow-sm">
                      <h3 className="font-medium text-primary">Available Tokens</h3>
                      <p className="text-lg font-semibold mt-2">{user?.token || "0"}</p>
                      <p className="text-sm text-muted-foreground mt-1">Use tokens to create videos</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Token History Card */}
          <div className="mt-6">
            <Card className="glass-effect">
              <CardHeader>
                <CardTitle>Token Usage History</CardTitle>
                <CardDescription>Track your token usage over time.</CardDescription>
              </CardHeader>
              <CardContent>
                <TokenHistory />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Profile;

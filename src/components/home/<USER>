import {Button} from "@/components/ui/button";
import {useNavigate} from "react-router-dom";

export const HeroSection = () => {
  const navigate = useNavigate();

  return (
    <div className="text-center space-y-6 max-w-3xl mx-auto mb-16">
      <h1 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/60 animate-fade-in">
        Create Professional Lecture Videos with Ease
      </h1>
      <p className="text-lg text-gray-600 animate-fade-in [animation-delay:200ms]">
        Transform your educational content into engaging video lectures using our advanced AI-powered platform. Upload your materials and let us handle the rest.
      </p>
      <div className="flex items-center justify-center gap-4 animate-fade-in [animation-delay:400ms]">
        <Button size="lg" onClick={() => navigate("/studio")}>
          Start Creating
        </Button>
        <Button variant="outline" size="lg" onClick={() => navigate("/session")}>
          View History
        </Button>
      </div>
    </div>
  );
};
